<?php
require_once '../db.php';
$pdo = getPDO();

try {
    // Récupérer les années à partir des dates d'accidents
    $stmt = $pdo->query("
        SELECT DISTINCT YEAR(dateAccident) AS annee, YEAR(dateAccident) AS id
        FROM accident
        WHERE dateAccident IS NOT NULL
        ORDER BY annee DESC
    ");
    $years = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Si aucune année n'est trouvée, générer les 5 dernières années
    if (empty($years)) {
        $currentYear = date('Y');
        $years = [];
        for ($i = 0; $i < 5; $i++) {
            $year = $currentYear - $i;
            $years[] = ['annee' => $year, 'id' => $year];
        }
    }

    header('Content-Type: application/json');
    echo json_encode($years);
} catch (Exception $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['error' => $e->getMessage()]);
}
