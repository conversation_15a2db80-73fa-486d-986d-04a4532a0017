<?php
require_once '../db.php';
$pdo = getPDO();

$id = (int) ($_POST['id'] ?? 0);

try {
  // Commencer une transaction
  $pdo->beginTransaction();

  // Supprimer d'abord les relations dans ACCIDENT_PERSONNE (si la table existe)
  try {
    $stmtPersonnes = $pdo->prepare("DELETE FROM ACCIDENT_PERSONNE WHERE idAccident = ?");
    $stmtPersonnes->execute([$id]);
  } catch (Exception $e) {
    // La table ACCIDENT_PERSONNE n'existe peut-être pas encore, continuer
  }

  // Supprimer l'accident
  $stmt = $pdo->prepare("DELETE FROM ACCIDENT WHERE id = ?");
  $stmt->execute([$id]);

  // Vérifier qu'un enregistrement a été supprimé
  if ($stmt->rowCount() > 0) {
    // Valider la transaction
    $pdo->commit();
    echo json_encode(['success' => true]);
  } else {
    // Annuler la transaction
    $pdo->rollBack();
    echo json_encode(['success' => false, 'error' => 'Aucun accident trouvé avec cet ID']);
  }
} catch (Exception $e) {
  // Annuler la transaction en cas d'erreur
  $pdo->rollBack();
  echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
