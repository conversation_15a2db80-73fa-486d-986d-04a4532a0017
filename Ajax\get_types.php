<?php
require_once '../db.php';
$pdo = getPDO();

try {
    $types = [
        ['id' => 1, 'typeAccident' => 'Accident'],
        ['id' => 4, 'typeAccident' => 'Accident de trajet'],
        ['id' => 5, 'typeAccident' => 'Premier soins'],
        ['id' => 2, 'typeAccident' => 'Presque accident'],
        ['id' => 3, 'typeAccident' => 'Situation dangereuse']
    ];

    header('Content-Type: application/json');
    echo json_encode($types);
} catch (Exception $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['error' => $e->getMessage()]);
}
