<?php
require_once '../db.php';

$pdo = getPDO();
$idAccident = (int) $_GET['idAccident'];

try {
    $stmt = $pdo->prepare("
        SELECT 
            AP.*,
            <PERSON><PERSON>, P.no<PERSON>,
            S.nomSecteur as secteurPersonne
        FROM ACCIDENT_PERSONNE AP
        LEFT JOIN PERSONNE P ON P.id = AP.idPersonne
        LEFT JOIN SECTEUR S ON S.id = P.idSecteur
        WHERE AP.idAccident = ?
        ORDER BY AP.id ASC
    ");
    $stmt->execute([$idAccident]);
    $personnes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($personnes);
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
