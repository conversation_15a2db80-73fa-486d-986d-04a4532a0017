<?php
require_once '../db.php';
$pdo = getPDO();

try {
  // Commencer une transaction
  $pdo->beginTransaction();

  $stmt = $pdo->prepare("
    UPDATE ACCIDENT SET
      dateAccident = ?, typeAccident = ?, causeAccident = ?,
      GMAO = ?, remarquesAccident = ?, secteurAccident = ?
    WHERE id = ?
  ");

  // Traitement des valeurs nulles ou vides
  $gmao = !empty($_POST['GMAO']) ? $_POST['GMAO'] : null;
  $secteurAccident = !empty($_POST['secteurAccident']) ? $_POST['secteurAccident'] : null;

  $stmt->execute([
    $_POST['dateAccident'],
    $_POST['typeAccident'],
    $_POST['causeAccident'],
    $gmao,
    $_POST['remarquesAccident'],
    $secteurAccident,
    $_POST['id']
  ]);

  // Supprimer les anciennes relations personnes
  $stmtDelete = $pdo->prepare("DELETE FROM ACCIDENT_PERSONNE WHERE idAccident = ?");
  $stmtDelete->execute([$_POST['id']]);

  // Gérer les personnes du nouveau format
  if (!empty($_POST['personnes']) && is_array($_POST['personnes'])) {
    $stmtPersonne = $pdo->prepare("
      INSERT INTO ACCIDENT_PERSONNE
        (idAccident, idPersonne, personneLibre, typeContrat, partieBlessée, joursArrets)
      VALUES
        (?, ?, ?, ?, ?, ?)
    ");

    foreach ($_POST['personnes'] as $personne) {
      // Vérifier qu'au moins une personne (ID ou libre) est fournie
      $idPersonne = !empty($personne['idPersonne']) ? $personne['idPersonne'] : null;
      $personneLibre = !empty($personne['personneLibre']) ? $personne['personneLibre'] : null;

      if ($idPersonne || $personneLibre) {
        $stmtPersonne->execute([
          $_POST['id'],
          $idPersonne,
          $personneLibre,
          !empty($personne['typeContrat']) ? $personne['typeContrat'] : null,
          !empty($personne['partieBlessée']) ? $personne['partieBlessée'] : null,
          !empty($personne['joursArrets']) ? $personne['joursArrets'] : null
        ]);
      }
    }
  }

  // Valider la transaction
  $pdo->commit();
  echo json_encode(['success' => true]);
} catch (Exception $e) {
  // Annuler la transaction en cas d'erreur
  $pdo->rollBack();
  echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
