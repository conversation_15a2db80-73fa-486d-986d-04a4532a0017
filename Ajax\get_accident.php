<?php
require_once '../db.php';

$pdo = getPDO();
$id = (int) $_GET['id'];

$stmt = $pdo->prepare("
    SELECT
        A.*,
        E.nomEquipement,
        S2.nomSecteur as secteurEquipement,
        S3.nomSecteur as secteurAccident
    FROM ACCIDENT A
    LEFT JOIN EQUIPEMENT E ON E.id = A.idEquipement
    LEFT JOIN SECTEUR S2 ON S2.id = E.idSecteur
    LEFT JOIN SECTEUR S3 ON S3.id = A.secteurAccident
    WHERE A.id = ?
");
$stmt->execute([$id]);
$accident = $stmt->fetch(PDO::FETCH_ASSOC);

// Récupérer les personnes liées à cet accident
$stmtPersonnes = $pdo->prepare("
    SELECT
        AP.*,
        P.prenomPerso, P.nomPerso,
        S.nomSecteur as secteurPersonne
    FROM ACCIDENT_PERSONNE AP
    LEFT JOIN PERSONNE P ON P.id = AP.idPersonne
    LEFT JOIN SECTEUR S ON S.id = P.idSecteur
    WHERE AP.idAccident = ?
    ORDER BY AP.id ASC
");
$stmtPersonnes->execute([$id]);
$personnesLiées = $stmtPersonnes->fetchAll(PDO::FETCH_ASSOC);

// Ajouter les personnes liées à l'accident
$accident['personnes_liées'] = $personnesLiées;

// Pour la compatibilité avec le formulaire, ajouter les données de la première personne
if (!empty($personnesLiées)) {
    $premièrePersonne = $personnesLiées[0];
    $accident['idPersonne'] = $premièrePersonne['idPersonne'];
    $accident['prenomPerso'] = $premièrePersonne['prenomPerso'];
    $accident['nomPerso'] = $premièrePersonne['nomPerso'];
    $accident['secteurPersonne'] = $premièrePersonne['secteurPersonne'];
    $accident['personneLibre'] = $premièrePersonne['personneLibre'];
    $accident['typeContrat'] = $premièrePersonne['typeContrat'];
    $accident['partieBlessée'] = $premièrePersonne['partieBlessée'];
}

echo json_encode($accident);
