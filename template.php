<?php
// template.php
?>
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><?= $title ?? 'Application Accident' ?></title>

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              50: '#f0f9ff',
              100: '#e0f2fe',
              200: '#bae6fd',
              300: '#7dd3fc',
              400: '#38bdf8',
              500: '#0ea5e9',
              600: '#0284c7',
              700: '#0369a1',
              800: '#075985',
              900: '#0c4a6e',
              950: '#082f49',
            },
          },
        },
      },
    }
  </script>

  <!-- Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Custom styles -->
  <style>
    [x-cloak] { display: none !important; }

    .fade-enter-active, .fade-leave-active {
      transition: opacity 0.3s;
    }
    .fade-enter, .fade-leave-to {
      opacity: 0;
    }

    /* Amélioration des datalists */
    input[list]::-webkit-calendar-picker-indicator {
      color: #0ea5e9;
    }

    /* Style pour les liens GMAO */
    .gmao-link {
      transition: all 0.2s ease;
    }
    .gmao-link:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <!-- Alpine.js -->
  <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

  <!-- jQuery (still needed for AJAX) -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

  <!-- SweetAlert2 for notifications -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

  <!-- Styles pour les datalists et selects améliorés -->
  <style>
    /* Amélioration des datalists */
    datalist {
      display: none; /* Masquer la datalist par défaut */
    }

    /* Style commun pour les inputs avec datalist et les selects */
    input[list], input.custom-input, select.custom-select {
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="%230ea5e9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
      background-repeat: no-repeat;
      background-position: right 0.5rem center;
      padding-right: 2rem;
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      transition: all 0.2s ease;
    }

    /* Focus styles */
    input[list]:focus, input.custom-input:focus, select.custom-select:focus {
      outline: none;
      border-color: #0ea5e9;
      box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
    }

    /* Hover styles */
    input[list]:hover, input.custom-input:hover, select.custom-select:hover {
      border-color: #0ea5e9;
    }

    /* Disabled styles */
    input[list]:disabled, input.custom-input:disabled, select.custom-select:disabled {
      background-color: #f3f4f6;
      cursor: not-allowed;
    }

    /* Placeholder styles */
    input[list]::placeholder, input.custom-input::placeholder {
      color: #9ca3af;
      opacity: 1;
    }

    /* Option styles */
    select.custom-select option {
      padding: 0.5rem;
      font-size: 0.875rem;
    }

    /* Filtres styles */
    #filters {
      transition: all 0.3s ease;
    }

    #filters:hover {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
  </style>

  <!-- Tippy.js for tooltips -->
  <script src="https://unpkg.com/@popperjs/core@2"></script>
  <script src="https://unpkg.com/tippy.js@6"></script>
  <link rel="stylesheet" href="https://unpkg.com/tippy.js@6/dist/tippy.css" />

  <!-- Header -->
  <header class="bg-primary-800 text-white shadow-md">
    <div class="container mx-auto px-4 py-2 flex justify-between items-center">
      <div class="flex items-center space-x-2">
        <i class="fas fa-clipboard-list text-xl"></i>
        <h1 class="text-xl font-bold">Gestion des Accidents</h1>
      </div>
    </div>
  </header>

  <!-- Main content -->
  <main class="container mx-auto px-4 py-4">
    <?= $content ?>
  </main>


  <script>
    // Initialize theme toggle
    document.addEventListener('DOMContentLoaded', function() {
      const themeToggle = document.getElementById('theme-toggle');
      const html = document.documentElement;

      // Check for saved theme preference or use system preference
      const isDark = localStorage.getItem('dark-mode') === 'true' ||
                    (!localStorage.getItem('dark-mode') &&
                     window.matchMedia('(prefers-color-scheme: dark)').matches);

      // Set initial theme
      if (isDark) {
        html.classList.add('dark');
        themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
      }

      // Toggle theme
      themeToggle.addEventListener('click', function() {
        if (html.classList.contains('dark')) {
          html.classList.remove('dark');
          localStorage.setItem('dark-mode', 'false');
          themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        } else {
          html.classList.add('dark');
          localStorage.setItem('dark-mode', 'true');
          themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        }
      });
    });
  </script>

  <!-- Datalists pour les options (chargées une seule fois) -->
  <datalist id="gmao-options">
    <!-- Options GMAO chargées dynamiquement au démarrage -->
  </datalist>

  <datalist id="personnes-options">
    <!-- Options personnes chargées dynamiquement -->
  </datalist>

  <datalist id="equipements-options">
    <!-- Options équipements chargées dynamiquement -->
  </datalist>
</body>
</html>
