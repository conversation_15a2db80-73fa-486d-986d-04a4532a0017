<?php
header('Content-Type: application/json');
require_once '../db.php';

$pdo = getPDO();

try {
  // Commencer une transaction
  $pdo->beginTransaction();

  $stmt = $pdo->prepare("
    INSERT INTO ACCIDENT
      (typeAccident, idEquipement, dateAccident, causeAccident, GMAO, remarquesAccident, secteurAccident)
    VALUES
      (?, ?, ?, ?, ?, ?, ?)
  ");

  // Traitement des valeurs nulles ou vides
  $gmao = !empty($_POST['GMAO']) ? $_POST['GMAO'] : null;
  $idEquipement = !empty($_POST['idEquipement']) ? $_POST['idEquipement'] : null;
  $secteurAccident = !empty($_POST['secteurAccident']) ? $_POST['secteurAccident'] : null;

  $stmt->execute([
    $_POST['typeAccident'],
    $idEquipement,
    $_POST['dateAccident'],
    $_POST['causeAccident'],
    $gmao,
    $_POST['remarquesAccident'],
    $secteurAccident
  ]);

  $accidentId = $pdo->lastInsertId();

  // Gérer les personnes du nouveau format
  if (!empty($_POST['personnes']) && is_array($_POST['personnes'])) {
    $stmtPersonne = $pdo->prepare("
      INSERT INTO ACCIDENT_PERSONNE
        (idAccident, idPersonne, personneLibre, typeContrat, partieBlessée, joursArrets)
      VALUES
        (?, ?, ?, ?, ?, ?)
    ");

    foreach ($_POST['personnes'] as $personne) {
      // Vérifier qu'au moins une personne (ID ou libre) est fournie
      $idPersonne = !empty($personne['idPersonne']) ? $personne['idPersonne'] : null;
      $personneLibre = !empty($personne['personneLibre']) ? $personne['personneLibre'] : null;

      if ($idPersonne || $personneLibre) {
        $stmtPersonne->execute([
          $accidentId,
          $idPersonne,
          $personneLibre,
          !empty($personne['typeContrat']) ? $personne['typeContrat'] : null,
          !empty($personne['partieBlessée']) ? $personne['partieBlessée'] : null,
          !empty($personne['joursArrets']) ? $personne['joursArrets'] : null
        ]);
      }
    }
  }

  // Valider la transaction
  $pdo->commit();
  echo json_encode(['success' => true]);
} catch (Exception $e) {
  // Annuler la transaction en cas d'erreur
  $pdo->rollBack();
  echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
