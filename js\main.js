// Fonction helper pour convertir les IDs de type en libellés
function getTypeLabel(typeId) {
    switch (parseInt(typeId)) {
        case 1: return 'Accident';
        case 4: return 'Accident de trajet';
        case 5: return 'Premier soins';
        case 2: return 'Presque accident';
        case 3: return 'Situation dangereuse';
        default: return 'Non défini';
    }
}

// Fonction pour auto-remplir le secteur depuis l'équipement sélectionné
function autoFillSecteurFromEquipement(equipementId, secteurSelectId) {
    const secteurSelect = document.getElementById(secteurSelectId);
    if (!secteurSelect || secteurSelect.value !== '') {
        // Ne pas écraser si un secteur est déjà sélectionné
        return;
    }

    // Trouver l'équipement dans les données
    const equipement = equipementsData.find(e => e.id == equipementId);
    if (!equipement || !equipement.secteurEquipement) {
        return;
    }

    // Trouver le secteur correspondant dans les données de secteurs
    const secteur = secteursData.find(s => s.nomSecteur === equipement.secteurEquipement);
    if (secteur) {
        secteurSelect.value = secteur.id;
        console.log(`Secteur auto-rempli: ${secteur.nomSecteur} (ID: ${secteur.id})`);
    }
}

// Variables globales pour stocker les données
let gmaoData = [];
let personnesData = [];
let equipementsData = [];
let secteursData = [];

// Fonction pour précharger toutes les données au démarrage
function preloadAllData() {
    console.log('Chargement des données...');

    // Afficher un indicateur de chargement
    const loadingToast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 15000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer);
            toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
    });

    loadingToast.fire({
        icon: 'info',
        title: 'Chargement des données...'
    });

    // Charger les GMAO
    $.ajax({
        url: 'ajax/get_gmao.php',
        dataType: 'json',
        timeout: 10000,
        success: function(data) {
            if (Array.isArray(data)) {
                gmaoData = data;
                populateDatalist('gmao-options', gmaoData, item => item.id, item => item.gmao_label || `GMAO-${item.id}`);
                console.log(`${gmaoData.length} GMAO chargées avec succès`);
            } else {
                console.error('Les données GMAO ne sont pas un tableau');
                generateFallbackData('gmao');
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Erreur lors du chargement des GMAO:', textStatus, errorThrown);
            generateFallbackData('gmao');
        }
    });

    // Charger les personnes
    $.ajax({
        url: 'ajax/get_personnes.php',
        dataType: 'json',
        timeout: 10000,
        success: function(data) {
            if (Array.isArray(data)) {
                personnesData = data;
                populateDatalist('personnes-options', personnesData, item => item.id, item => `${item.prenomPerso} ${item.nomPerso}`);
                console.log(`${personnesData.length} personnes chargées avec succès`);
            } else {
                console.error('Les données personnes ne sont pas un tableau');
                generateFallbackData('personnes');
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Erreur lors du chargement des personnes:', textStatus, errorThrown);
            generateFallbackData('personnes');
        }
    });

    // Charger les équipements
    $.ajax({
        url: 'ajax/get_equipements.php',
        dataType: 'json',
        timeout: 10000,
        success: function(data) {
            if (Array.isArray(data)) {
                equipementsData = data;
                populateDatalist('equipements-options', equipementsData, item => item.id, item => {
                    // Inclure le secteur s'il existe, séparé par un tiret
                    return item.secteurEquipement
                        ? `${item.nomEquipement} - ${item.secteurEquipement}`
                        : item.nomEquipement;
                });
                console.log(`${equipementsData.length} équipements chargés avec succès`);
            } else {
                console.error('Les données équipements ne sont pas un tableau');
                generateFallbackData('equipements');
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Erreur lors du chargement des équipements:', textStatus, errorThrown);
            generateFallbackData('equipements');
        }
    });

    // Charger les secteurs
    $.ajax({
        url: 'ajax/get_secteurs.php',
        dataType: 'json',
        timeout: 10000,
        success: function(data) {
            if (Array.isArray(data)) {
                secteursData = data;
                populateSecteurSelects();
                console.log(`${secteursData.length} secteurs chargés avec succès`);
                loadingToast.close();
            } else {
                console.error('Les données secteurs ne sont pas un tableau');
                generateFallbackData('secteurs');
                loadingToast.close();
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Erreur lors du chargement des secteurs:', textStatus, errorThrown);
            generateFallbackData('secteurs');
            loadingToast.close();
        }
    });
}

// Fonction pour générer des données de secours
function generateFallbackData(type) {
    console.log(`Génération de ${type} de secours`);

    switch(type) {
        case 'gmao':
            gmaoData = [];
            for (let i = 1; i <= 50; i++) {
                gmaoData.push({
                    id: i,
                    gmao_id: i,
                    gmao_label: `GMAO-${i}`
                });
            }
            populateDatalist('gmao-options', gmaoData, item => item.id, item => item.gmao_label);
            break;

        case 'personnes':
            personnesData = [];
            for (let i = 1; i <= 10; i++) {
                personnesData.push({
                    id: i,
                    prenomPerso: `Prénom${i}`,
                    nomPerso: `Nom${i}`
                });
            }
            populateDatalist('personnes-options', personnesData, item => item.id, item => `${item.prenomPerso} ${item.nomPerso}`);
            break;

        case 'equipements':
            equipementsData = [];
            for (let i = 1; i <= 10; i++) {
                equipementsData.push({
                    id: i,
                    nomEquipement: `Équipement ${i}`,
                    secteurEquipement: `Secteur ${Math.ceil(i/3)}`
                });
            }
            populateDatalist('equipements-options', equipementsData, item => item.id, item => {
                // Inclure le secteur séparé par un tiret
                return `${item.nomEquipement} - ${item.secteurEquipement}`;
            });
            break;

        case 'secteurs':
            secteursData = [];
            for (let i = 1; i <= 5; i++) {
                secteursData.push({
                    id: i,
                    nomSecteur: `Secteur ${i}`
                });
            }
            populateSecteurSelects();
            break;
    }
}

// Fonction pour remplir les selects de secteurs
function populateSecteurSelects() {
    const secteurSelects = ['secteurAccident', 'edit-secteurAccident'];

    secteurSelects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // Vider le select
            select.innerHTML = '<option value="">Sélectionner...</option>';

            // Ajouter les options
            secteursData.forEach(secteur => {
                const option = document.createElement('option');
                option.value = secteur.id;
                option.textContent = secteur.nomSecteur;
                select.appendChild(option);
            });
        }
    });
}

// Fonction générique pour remplir une datalist
function populateDatalist(datalistId, data, valueGetter, labelGetter) {
    const datalist = document.getElementById(datalistId);
    if (!datalist) {
        console.error(`Datalist avec ID ${datalistId} non trouvée`);
        return;
    }

    console.log(`Remplissage de la datalist ${datalistId} avec ${data.length} éléments`);

    // Vider la datalist
    datalist.innerHTML = '';

    // Ajouter les options
    data.forEach(item => {
        // Pour les personnes et équipements, on veut afficher le texte descriptif
        if (datalistId === 'personnes-options' || datalistId === 'equipements-options') {
            const label = labelGetter(item);  // Texte descriptif (nom ou équipement)
            const id = valueGetter(item);     // ID numérique

            // Créer l'option avec le texte comme valeur visible
            const optionHtml = `<option value="${label}" data-id="${id}">${label}</option>`;
            datalist.insertAdjacentHTML('beforeend', optionHtml);

            // Log pour débogage
            if (datalistId === 'personnes-options') {
                console.log(`Personne ajoutée: ID=${id}, Label=${label}`);
            } else {
                console.log(`Équipement ajouté: ID=${id}, Label=${label}`);
            }
        }
        // Pour les GMAO, on veut que la valeur soit l'ID mais que le texte affiché soit le libellé
        else if (datalistId === 'gmao-options') {
            const id = valueGetter(item);
            const label = labelGetter(item);

            // Créer l'option avec l'ID comme valeur
            const optionHtml = `<option value="${id}" data-label="${label}">${label}</option>`;
            datalist.insertAdjacentHTML('beforeend', optionHtml);
        }
        // Pour les autres cas
        else {
            const value = valueGetter(item);
            const label = labelGetter(item);

            // Créer l'option standard
            const optionHtml = `<option value="${value}">${label}</option>`;
            datalist.insertAdjacentHTML('beforeend', optionHtml);
        }
    });

    // Vérifier que les options ont été ajoutées correctement
    console.log(`${datalistId} contient maintenant ${datalist.children.length} options`);
}

$(document).ready(function () {
    // Configuration des notifications Toast
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        iconColor: '#0ea5e9',
        customClass: {
            popup: 'bg-white shadow-md rounded-lg'
        }
    });

    // Précharger toutes les données
    preloadAllData();

    // Initialiser les filtres simples
    initSimpleFilters();

    // Vérifier les datalists après un court délai pour s'assurer qu'elles sont chargées
    setTimeout(() => {
        console.log("=== VÉRIFICATION DES DATALISTS ===");

        // Vérifier la datalist des personnes
        const personnesDatalist = document.getElementById('personnes-options');
        if (personnesDatalist) {
            console.log(`Datalist personnes: ${personnesDatalist.children.length} options`);
            if (personnesDatalist.children.length > 0) {
                const firstOption = personnesDatalist.children[0];
                console.log(`Premier élément: value="${firstOption.value}", data-id="${firstOption.getAttribute('data-id')}"`);
            }
        } else {
            console.error("Datalist personnes-options non trouvée");
        }

        // Vérifier la datalist des équipements
        const equipementsDatalist = document.getElementById('equipements-options');
        if (equipementsDatalist) {
            console.log(`Datalist équipements: ${equipementsDatalist.children.length} options`);
            if (equipementsDatalist.children.length > 0) {
                const firstOption = equipementsDatalist.children[0];
                console.log(`Premier élément: value="${firstOption.value}", data-id="${firstOption.getAttribute('data-id')}"`);
            }
        } else {
            console.error("Datalist equipements-options non trouvée");
        }
    }, 2000);

    // Initialisation
    loadAccidents();
    enableTooltips();

    // Fonction pour initialiser les filtres simples (select normaux)
    function initSimpleFilters() {
        // Charger les types, années et secteurs dans les selects normaux
        loadSelectData('ajax/get_types.php', 'filterType', t => t.typeAccident);
        loadSelectData('ajax/get_annees.php', 'filterAnnee', a => a.annee);
        loadSelectData('ajax/get_secteurs.php', 'filterSecteur', s => s.nomSecteur);
    }

    // Activer les tooltips avec Tippy.js (alternative à Bootstrap tooltips)
    function enableTooltips() {
        // Utiliser une approche compatible avec Tailwind sans dépendre de Bootstrap
        if (typeof tippy !== 'undefined') {
            // Si tippy.js est disponible
            tippy('[data-tippy-content]');
        } else {
            // Fallback simple si tippy n'est pas disponible
            $('[data-bs-toggle="tooltip"]').each(function() {
                const title = $(this).attr('title') || $(this).data('bs-title') || $(this).data('bs-original-title');
                if (title) {
                    $(this).attr('data-tippy-content', title);
                    $(this).removeAttr('data-bs-toggle');
                }
            });

            // Ajouter Tippy.js dynamiquement si nécessaire
            if ($('[data-tippy-content]').length > 0 && typeof tippy === 'undefined') {
                const tippyScript = document.createElement('script');
                tippyScript.src = 'https://unpkg.com/@popperjs/core@2';
                document.head.appendChild(tippyScript);

                const tippyMainScript = document.createElement('script');
                tippyMainScript.src = 'https://unpkg.com/tippy.js@6';
                tippyMainScript.onload = function() {
                    tippy('[data-tippy-content]');
                };
                document.head.appendChild(tippyMainScript);
            }
        }
    }

    // Charger les accidents avec indicateur de chargement
    function loadAccidents(queryParams = '') {
        $('#loading-indicator').removeClass('hidden');
        $('#accident-container').addClass('opacity-50');

        $.get('ajax/get_accidents.php' + queryParams, function (html) {
            $('#accident-container').html(html).removeClass('opacity-50');
            $('#loading-indicator').addClass('hidden');
            enableTooltips();
        }).fail(function() {
            $('#loading-indicator').addClass('hidden');
            $('#accident-container').removeClass('opacity-50');
            Toast.fire({
                icon: 'error',
                title: 'Erreur lors du chargement des données'
            });
        });
    }

    // Charger options dans <select> (version simplifiée sans Tom Select)
    function loadSelectData(url, selectId, labelBuilder) {
        // Vérifier si l'élément existe
        const select = document.getElementById(selectId);
        if (!select) {
            console.warn(`L'élément avec l'ID ${selectId} n'existe pas.`);
            return;
        }

        // Ajouter une option de chargement
        const $select = $('#' + selectId);
        $select.html('<option value="">Chargement...</option>');

        // Charger les données
        $.ajax({
            url: url,
            dataType: 'json',
            timeout: 5000, // 5 secondes de timeout
            success: function(data) {
                try {
                    // Vérifier si les données sont valides
                    if (!Array.isArray(data)) {
                        throw new Error('Les données ne sont pas un tableau');
                    }

                    // Pour les selects normaux
                    $select.html('<option value="">Sélectionner...</option>');

                    // Ajouter les options
                    $.each(data, function(_, item) {
                        if (item && item.id !== undefined) {
                            const option = $('<option>').val(item.id).text(labelBuilder(item));
                            $select.append(option);
                        }
                    });
                } catch (error) {
                    console.warn('Erreur dans loadSelectData:', error);

                    // Fallback simple en cas d'erreur
                    $select.html('<option value="">Sélectionner...</option>');
                }
            },
            error: function(_, textStatus, errorThrown) {
                console.error('Erreur lors du chargement des données:', textStatus, errorThrown);

                // Fallback en cas d'erreur AJAX
                $select.html('<option value="">Sélectionner...</option>');
            }
        });
    }

    // Réinitialiser les filtres
    $('#resetFilters').on('click', function() {
        // Réinitialiser les selects normaux
        document.querySelectorAll('.filter-select').forEach(select => {
            select.value = '';
        });

        // Réinitialiser les inputs avec datalist
        document.getElementById('filterPersonne').value = '';
        document.getElementById('filterEquipement').value = '';

        // Recharger les accidents sans filtres
        loadAccidents();
    });

    // Gestion de l'ouverture de la modale d'ajout
    document.getElementById('addAccidentBtn').addEventListener('click', function() {
        // Charger les selects simples
        loadSelectData('ajax/get_types.php', 'typeAccident', t => t.typeAccident);

        // Les datalists sont déjà chargées au démarrage
        // Réinitialiser le formulaire
        $('#addAccidentForm')[0].reset();
    });

    // Note: Les gestionnaires pour les champs personnes et équipements
    // sont maintenant gérés dans les modals individuels car ils utilisent
    // une structure dynamique avec plusieurs personnes

    // Soumission formulaire d'ajout (utilisation de la délégation d'événements)
    $(document).on('submit', '#addAccidentForm', function (e) {
        e.preventDefault();
        console.log('🚀 Soumission du formulaire d\'ajout interceptée');

        const form = this;
        const formData = new FormData(form);

        // Debug: Afficher les données du formulaire
        console.log('📋 Données du formulaire:');
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}: ${value}`);
        }

        // Afficher indicateur de chargement dans le bouton
        const submitBtn = $(form).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-1"></i> Traitement...').prop('disabled', true);

        $.ajax({
            url: 'Ajax/add_accident.php',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('✅ Réponse du serveur:', response);

                // Vérifier si la réponse est du JSON valide
                let data;
                try {
                    data = typeof response === 'string' ? JSON.parse(response) : response;
                } catch (e) {
                    console.error('❌ Erreur de parsing JSON:', e);
                    console.error('📄 Réponse brute:', response);
                    throw new Error('Réponse serveur invalide');
                }

                if (data.success) {
                    Toast.fire({
                        icon: 'success',
                        title: 'L\'accident a été ajouté avec succès'
                    });

                    // Réinitialiser le formulaire
                    form.reset();

                    // Fermer le modal
                    document.getElementById('addAccidentModal').classList.add('hidden');

                    // Réinitialiser le conteneur des personnes
                    if (typeof resetPersonnesContainer === 'function') {
                        resetPersonnesContainer();
                    }

                    // Recharger la liste des accidents
                    loadAccidents();

                    console.log('✅ Accident ajouté avec succès');
                } else {
                    console.error('❌ Erreur serveur:', data.error);
                    Toast.fire({
                        icon: 'error',
                        title: data.error || 'Une erreur est survenue lors de l\'ajout'
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('❌ Erreur AJAX:', {xhr, status, error});
                console.error('📄 Réponse complète:', xhr.responseText);

                Toast.fire({
                    icon: 'error',
                    title: 'Erreur de connexion: ' + error
                });
            },
            complete: function() {
                // Restaurer le bouton dans tous les cas
                submitBtn.html(originalText).prop('disabled', false);
                console.log('🔄 Bouton restauré');
            }
        });
    });

    // Gestion du clic sur bouton modifier
    $(document).on('click', '.edit-btn', function () {
        const id = $(this).data('id');
        document.getElementById('editAccidentModal').classList.remove('hidden');

        // Afficher indicateur de chargement
        $('#edit-id').closest('form').find('input, select, textarea').prop('disabled', true);

        // Charger les selects simples
        loadSelectData('ajax/get_types.php', 'edit-type', t => t.typeAccident);

        $.getJSON(`ajax/get_accident.php?id=${id}`, function (data) {
            // Remplir les champs du formulaire
            $('#edit-id').val(data.id);
            $('#edit-date').val(data.dateAccident);

            // Gérer le select pour le type (valeur numérique)
            const typeSelect = document.getElementById('edit-type');
            if (typeSelect) {
                // Définir la valeur du type (maintenant c'est un ID numérique)
                typeSelect.value = data.typeAccident;
            }

            // Remplir les champs texte
            $('#edit-cause').val(data.causeAccident);
            $('#edit-remarques').val(data.remarquesAccident);

            // Charger les personnes liées dans le modal d'édition
            if (typeof loadEditPersonnes === 'function') {
                loadEditPersonnes(data.personnes_liées || []);
            }

            // Remplir le secteur accident
            $('#edit-secteurAccident').val(data.secteurAccident || '');

            // Définir la valeur GMAO dans l'input
            const gmaoInput = document.getElementById('edit-gmao');
            if (gmaoInput) {
                gmaoInput.value = data.GMAO || '';
            }

            // Définir les valeurs pour personne et équipement
            const personneInput = document.getElementById('edit-personne');
            const personneTextInput = document.getElementById('edit-personne-text');

            if (personneInput && personneTextInput && data.idPersonne) {
                // Stocker l'ID dans le champ caché
                personneInput.value = data.idPersonne;

                // Trouver la personne correspondante dans les données préchargées
                const personne = personnesData.find(p => p.id == data.idPersonne);
                if (personne) {
                    // Afficher le nom complet dans le champ texte
                    const nomComplet = `${personne.prenomPerso} ${personne.nomPerso}`;
                    personneTextInput.value = nomComplet;
                }
            }

            const equipementInput = document.getElementById('edit-equipement');
            const equipementTextInput = document.getElementById('edit-equipement-text');

            if (equipementInput && equipementTextInput && data.idEquipement) {
                // Stocker l'ID dans le champ caché
                equipementInput.value = data.idEquipement;

                // Trouver l'équipement correspondant dans les données préchargées
                const equipement = equipementsData.find(e => e.id == data.idEquipement);
                if (equipement) {
                    // Afficher le nom de l'équipement avec son secteur dans le champ texte
                    equipementTextInput.value = equipement.secteurEquipement
                        ? `${equipement.nomEquipement} - ${equipement.secteurEquipement}`
                        : equipement.nomEquipement;
                }
            }

            // Réactiver les champs
            $('#edit-id').closest('form').find('input, select, textarea').prop('disabled', false);
        }).fail(function() {
            Toast.fire({
                icon: 'error',
                title: 'Erreur lors du chargement des données'
            });
            document.getElementById('editAccidentModal').classList.add('hidden');
        });
    });

    // Gestionnaire pour les champs texte avec datalist (formulaire d'édition)
    document.getElementById('edit-personne-text').addEventListener('input', function() {
        const selectedText = this.value;
        console.log(`[EDIT] Texte sélectionné pour personne: "${selectedText}"`);

        const options = document.querySelectorAll('#personnes-options option');
        console.log(`[EDIT] Nombre d'options disponibles: ${options.length}`);

        let found = false;

        // Chercher l'option correspondante
        for (const option of options) {
            console.log(`[EDIT] Comparaison avec option: value="${option.value}", data-id="${option.getAttribute('data-id')}"`);

            if (option.value === selectedText) {
                const id = option.getAttribute('data-id');
                console.log(`[EDIT] Correspondance trouvée! ID=${id}`);

                // Mettre à jour le champ caché avec l'ID
                document.getElementById('edit-personne').value = id;
                found = true;
                break;
            }
        }

        // Si aucune correspondance, effacer l'ID
        if (!found) {
            console.log("[EDIT] Aucune correspondance trouvée, effacement de l'ID");
            document.getElementById('edit-personne').value = '';
        }
    });

    document.getElementById('edit-equipement-text').addEventListener('input', function() {
        const selectedText = this.value;
        console.log(`[EDIT] Texte sélectionné pour équipement: "${selectedText}"`);

        const options = document.querySelectorAll('#equipements-options option');
        console.log(`[EDIT] Nombre d'options disponibles: ${options.length}`);

        let found = false;

        // Chercher l'option correspondante
        for (const option of options) {
            console.log(`[EDIT] Comparaison avec option: value="${option.value}", data-id="${option.getAttribute('data-id')}"`);

            // Vérifier si le texte sélectionné correspond à la valeur de l'option
            // La valeur contient maintenant le nom de l'équipement et son secteur
            if (option.value === selectedText) {
                const id = option.getAttribute('data-id');
                console.log(`[EDIT] Correspondance trouvée! ID=${id}`);

                // Mettre à jour le champ caché avec l'ID
                document.getElementById('edit-equipement').value = id;

                // Auto-remplir le secteur de l'accident si pas déjà sélectionné
                autoFillSecteurFromEquipement(id, 'edit-secteurAccident');

                found = true;
                break;
            }
        }

        // Si aucune correspondance, effacer l'ID
        if (!found) {
            console.log("[EDIT] Aucune correspondance trouvée, effacement de l'ID");
            document.getElementById('edit-equipement').value = '';
        }
    });

    // Ajouter des gestionnaires d'événements pour la sélection (change)
    document.getElementById('edit-personne-text').addEventListener('change', function() {
        console.log(`[EDIT] Changement détecté pour personne: "${this.value}"`);
        // Déclencher l'événement input pour traiter la sélection
        this.dispatchEvent(new Event('input'));
    });

    document.getElementById('edit-equipement-text').addEventListener('change', function() {
        console.log(`[EDIT] Changement détecté pour équipement: "${this.value}"`);
        // Déclencher l'événement input pour traiter la sélection
        this.dispatchEvent(new Event('input'));
    });

    // Soumission formulaire de modification
    $('#editAccidentForm').on('submit', function (e) {
        e.preventDefault();
        const formData = new FormData(this);

        // Afficher indicateur de chargement dans le bouton
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-1"></i> Traitement...').prop('disabled', true);

        $.ajax({
            url: 'ajax/update_accident.php',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success() {
                Toast.fire({
                    icon: 'success',
                    title: 'Les données ont été mises à jour avec succès'
                });
                document.getElementById('editAccidentModal').classList.add('hidden');
                loadAccidents();
            },
            error() {
                Toast.fire({
                    icon: 'error',
                    title: 'Une erreur est survenue lors de la mise à jour'
                });
            },
            complete() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Gestion suppression avec confirmation
    $(document).on('click', '.delete-btn', function () {
        const id = $(this).data('id');

        Swal.fire({
            title: 'Supprimer cet accident ?',
            text: 'Cette action est irréversible.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            customClass: {
                confirmButton: 'px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 mr-2',
                cancelButton: 'px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
            },
        }).then(result => {
            if (result.isConfirmed) {
                // Afficher indicateur de chargement
                Swal.fire({
                    title: 'Suppression en cours...',
                    text: 'Veuillez patienter',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.post('ajax/delete_accident.php', { id }, function (data) {
                    if (data.success) {
                        Swal.fire({
                            title: 'Supprimé !',
                            text: 'L\'accident a été supprimé avec succès.',
                            icon: 'success',
                            confirmButtonColor: '#0ea5e9'
                        });
                        loadAccidents();
                    } else {
                        Swal.fire({
                            title: 'Erreur',
                            text: data.error || 'Impossible de supprimer cet accident.',
                            icon: 'error',
                            confirmButtonColor: '#0ea5e9'
                        });
                    }
                }, 'json').fail(function() {
                    Swal.fire({
                        title: 'Erreur',
                        text: 'Une erreur de connexion est survenue.',
                        icon: 'error',
                        confirmButtonColor: '#0ea5e9'
                    });
                });
            }
        });
    });

    // Charger les données pour les filtres
    function initializeFilters() {
        // Attendre que le DOM soit complètement chargé
        setTimeout(() => {
            // Charger les données des filtres simples (selects normaux)
            loadSelectData('ajax/get_types.php', 'filterType', t => t.typeAccident);
            loadSelectData('ajax/get_annees.php', 'filterAnnee', a => a.annee);
            loadSelectData('ajax/get_secteurs.php', 'filterSecteur', s => s.nomSecteur);

            // Les datalists pour personnes et équipements sont déjà chargées au démarrage
            // via la fonction preloadAllData()
        }, 100);
    }

    // Initialiser les filtres
    initializeFilters();

    // Gestion des filtres avec debounce pour éviter trop de requêtes
    let filterTimeout;

    // Fonction pour appliquer les filtres
    function applyFilters() {
        clearTimeout(filterTimeout);

        filterTimeout = setTimeout(function() {
            // Pour les filtres de personne et équipement, on doit chercher l'ID correspondant au texte
            let personneId = '';
            let equipementId = '';

            // Récupérer le texte saisi
            const personneText = $('#filterPersonne').val();
            const equipementText = $('#filterEquipement').val();

            console.log(`Filtrage - Personne: "${personneText}", Équipement: "${equipementText}"`);

            // Chercher l'ID correspondant pour la personne
            if (personneText) {
                // Utiliser une requête plus précise pour éviter les problèmes d'échappement
                const personneOptions = document.querySelectorAll('#personnes-options option');
                for (const option of personneOptions) {
                    if (option.value === personneText) {
                        personneId = option.getAttribute('data-id');
                        console.log(`ID personne trouvé: ${personneId}`);
                        break;
                    }
                }
            }

            // Chercher l'ID correspondant pour l'équipement
            if (equipementText) {
                // Utiliser une requête plus précise pour éviter les problèmes d'échappement
                const equipementOptions = document.querySelectorAll('#equipements-options option');
                for (const option of equipementOptions) {
                    // La valeur de l'option contient maintenant le nom de l'équipement et son secteur
                    if (option.value === equipementText) {
                        equipementId = option.getAttribute('data-id');
                        console.log(`ID équipement trouvé: ${equipementId}`);
                        break;
                    }
                }
            }

            // Construire les paramètres avec les IDs pour personne et équipement
            const params = {
                personne: personneId,
                equipement: equipementId,
                type: $('#filterType').val(),
                annee: $('#filterAnnee').val(),
                secteur: $('#filterSecteur').val()
            };

            console.log('Paramètres de filtrage:', params);

            const query = Object.entries(params)
                .filter(([_, val]) => val)
                .map(([k, v]) => `${k}=${encodeURIComponent(v)}`)
                .join('&');

            loadAccidents(query ? '?' + query : '');
        }, 300);
    }

    // Écouter les changements sur tous les filtres (selects et inputs)
    $('.filter-select').on('change', applyFilters);
    $('#filterPersonne, #filterEquipement').on('input', applyFilters);

    // Afficher les détails d'un accident
    $(document).on('click', '.view-details-btn', function() {
        const id = $(this).data('id');

        $.getJSON(`ajax/get_accident.php?id=${id}`, function(data) {
            Swal.fire({
                title: 'Détails de l\'accident',
                html: `
                    <div class="text-left">
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Date</p>
                                <p class="text-sm">${new Date(data.dateAccident).toLocaleDateString('fr-FR')}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Type</p>
                                <p class="text-sm">${getTypeLabel(data.typeAccident)}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Personne</p>
                                ${data.prenomPerso && data.nomPerso ?
                                    `<p class="text-sm">${data.prenomPerso} ${data.nomPerso}</p>
                                     ${data.secteurPersonne ? `<p class="text-xs text-gray-500">Secteur: ${data.secteurPersonne}</p>` : ''}
                                     ${data.typeContrat ? `<p class="text-xs text-blue-600">Contrat: ${data.typeContrat}</p>` : ''}` :
                                    data.personneLibre ?
                                        `<p class="text-sm">${data.personneLibre}</p><p class="text-xs text-gray-500">Personne libre</p>` :
                                        '<p class="text-sm">-</p>'
                                }
                                ${data.partieBlessée ? `<p class="text-xs text-red-600">Partie blessée: ${data.partieBlessée}</p>` : ''}
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Équipement</p>
                                <p class="text-sm">${data.nomEquipement || '-'}</p>
                                ${data.secteurEquipement ? `<p class="text-xs text-gray-500">Secteur équipement: ${data.secteurEquipement}</p>` : ''}
                                ${data.secteurAccident && data.secteurAccident !== data.secteurEquipement ? `<p class="text-xs text-blue-600">Secteur accident: ${data.secteurAccident}</p>` : ''}
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Jours d'arrêt</p>
                                <p class="text-sm">${data.joursArrets > 0 ? data.joursArrets : '-'}</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">GMAO</p>
                                ${data.GMAO ?
                                    `<p class="text-sm">
                                        <a href="https://frscmgmao.scmlemans.com/GMAO/detailDemande.php?id=${data.GMAO}"
                                           target="_blank"
                                           class="text-primary-600 hover:text-primary-800 transition-colors">
                                            ${data.GMAO} <i class="fas fa-external-link-alt text-xs"></i>
                                        </a>
                                    </p>` :
                                    '<p class="text-sm">-</p>'
                                }
                            </div>
                        </div>
                        <div class="mb-4">
                            <p class="text-sm font-medium text-gray-500">Cause</p>
                            <p class="text-sm">${data.causeAccident || '-'}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Remarques</p>
                            <p class="text-sm">${data.remarquesAccident || '-'}</p>
                        </div>
                    </div>
                `,
                confirmButtonText: 'Fermer',
                confirmButtonColor: '#0ea5e9',
                customClass: {
                    container: 'swal-wide',
                    popup: 'rounded-lg',
                    confirmButton: 'px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                }
            });
        }).fail(function() {
            Toast.fire({
                icon: 'error',
                title: 'Erreur lors du chargement des détails'
            });
        });
    });
});
