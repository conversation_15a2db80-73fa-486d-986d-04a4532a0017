<?php
require_once '../db.php';
$pdo = getPDO();

// Créer un tableau pour stocker les GMAO
$gmao = [];

// Récupérer les GMAO réelles si possible
try {
    // Récupérer les GMAO depuis la table demande (plus efficace)
    $stmt = $pdo->query("
        SELECT 
            id, 
            id as gmao_id, 
            id as gmao_label
        FROM demande
        WHERE id IS NOT NULL
        ORDER BY id DESC
    ");
    
    if ($stmt) {
        $gmao = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Si aucune GMAO n'est trouvée dans la table demande, essayer la table accident
    if (empty($gmao)) {
        $stmt2 = $pdo->query("
            SELECT DISTINCT 
                GMAO as id, 
                GMAO as gmao_id, 
                CONCAT('GMAO-', GMAO) as gmao_label
            FROM accident
            WHERE GMAO IS NOT NULL AND GMAO > 0
            ORDER BY GMAO DESC
        ");
        
        if ($stmt2) {
            $gmao = $stmt2->fetchAll(PDO::FETCH_ASSOC);
        }
    }
} catch (Exception $e) {
    // En cas d'erreur, logger l'erreur
    error_log("Erreur lors de la récupération des GMAO: " . $e->getMessage());
}

// Si toujours vide, générer des exemples
if (empty($gmao)) {
    for ($i = 1; $i <= 100; $i++) {
        $gmao[] = [
            'id' => $i,
            'gmao_id' => $i,
            'gmao_label' => "GMAO-$i"
        ];
    }
}

// Renvoyer les données au format JSON
header('Content-Type: application/json');
echo json_encode($gmao);
